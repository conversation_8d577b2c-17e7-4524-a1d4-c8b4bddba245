{"name": "erh-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "resolutions": {"string-width": "4.2.3"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "build:prod": "npx prisma generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npx prisma generate && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "npx prisma generate && nest build", "start:prod:pm2": "node dist/main.js", "documentation:serve": "npx @compodoc/compodoc -p tsconfig.json --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.14", "@nestjs/core": "^11.0.14", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.14", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.1.1", "@prisma/client": "^6.6.0", "@types/multer": "^1.4.12", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "exceljs": "^4.4.0", "fast-csv": "^5.0.2", "fs-extra": "^11.3.0", "google-spreadsheet": "^4.1.4", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "react-scripts": "^5.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "stripe": "^18.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.4", "@nestjs/testing": "^11.0.14", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "prettier": "^3.5.3", "prisma": "^6.6.0", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}