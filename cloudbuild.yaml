steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '-t'
      - 'us-central1-docker.pkg.dev/eremote-hire-website-405811/erh-backend/erh-backend'
      - '--cache-from'
      - 'us-central1-docker.pkg.dev/eremote-hire-website-405811/erh-backend/erh-backend:latest'
      - .
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - 'us-central1-docker.pkg.dev/eremote-hire-website-405811/erh-backend/erh-backend'
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - run
      - deploy
      - erh-backend
      - '--image'
      - 'us-central1-docker.pkg.dev/eremote-hire-website-405811/erh-backend/erh-backend'
      - '--region'
      - us-central1
      - '--platform'
      - $_PLATFORM
    entrypoint: gcloud
timeout: 1200s
images:
  - 'us-central1-docker.pkg.dev/eremote-hire-website-405811/erh-backend/erh-backend'
options:
  logging: CLOUD_LOGGING_ONLY
