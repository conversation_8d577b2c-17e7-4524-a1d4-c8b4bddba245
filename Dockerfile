FROM node:20-alpine as builder
RUN apk add --no-cache openssl
ENV NODE_ENV build
RUN npm i bun -g
RUN npm install -g @nestjs/cli

USER node
WORKDIR /home/<USER>

COPY package*.json ./
RUN bun i --production

COPY --chown=node:node . .
RUN npx prisma generate \
    && npm run build

FROM node:20-alpine
RUN apk add --no-cache openssl

ENV NODE_ENV production

ENV PORT=3000

USER node
WORKDIR /home/<USER>

COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/
COPY --from=builder --chown=node:node /home/<USER>/eremote-hire-website-cloud.json ./eremote-hire-website-cloud.json
EXPOSE 3000

CMD ["node", "dist/src/main.js"]