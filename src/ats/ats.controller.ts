import {
    Post,
    Body,
    UploadedFiles,
    UseInterceptors,
    HttpException,
    HttpStatus,
    Controller,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AtsService } from './ats.service';
import { Constants, endpoints } from 'src/utils/constants';
import * as multer from 'multer';

@Controller(endpoints.ats)
export class AtsController {
    constructor(private readonly atsService: AtsService) { }

    @Post('/add')
    @UseInterceptors(
        FilesInterceptor(Constants.files, 10, {}),
    )
    async evaluate(
        @Body() body: any,
        @UploadedFiles() files: Express.Multer.File[],
    ) {
        const { job_description, option } = body;

        if (!files || files.length === 0) {
            console.error('No files provided.');
            throw new HttpException('Missing files.', HttpStatus.BAD_REQUEST);
        }

        if (!job_description || !option) {
            console.error('Missing required fields in request body.');
            throw new HttpException('Missing required fields.', HttpStatus.BAD_REQUEST);
        }


        const storage = multer.memoryStorage();
        const fileFilter = (req, file, callback) => {
            if (file.mimetype !== 'application/pdf') {
                return callback(
                    new HttpException('Only PDF files are allowed.', HttpStatus.BAD_REQUEST),
                    false
                );
            }
            callback(null, true);
        };

        try {
            const result = await this.atsService.evaluateFiles(files, job_description, option);
            return result;
        } catch (error) {
            console.error('Error during evaluation:', error.message);
            throw new HttpException('An error occurred while evaluating files.', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
