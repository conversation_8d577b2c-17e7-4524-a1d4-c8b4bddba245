import * as FormData from 'form-data';
import * as axios from 'axios';

import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { Storage } from '@google-cloud/storage';
import { sendMail } from 'src/utils/functions';

@Injectable()
export class AtsService {
    private readonly storage: Storage;
    private readonly bucketName = 'eremotehire';

    constructor(private readonly mailerService: MailerService) {
        this.storage = new Storage({
            keyFilename: 'eremote-hire-website-cloud.json',
        });
    }

    async uploadToGCS(fileBuffer: Buffer, fileName: string): Promise<string> {
        const bucket = this.storage.bucket(this.bucketName);
        const gcsFile = bucket.file(`ATS/${fileName}`);

        await gcsFile.save(fileBuffer, {
            contentType: 'application/pdf',
            resumable: false,
        });

        const publicUrl = `https://storage.googleapis.com/${this.bucketName}/ATS/${fileName}`;
        return publicUrl;
    }

    async evaluateFiles(files: Express.Multer.File[], jobDescription: string, option: string) {
        const formData = new FormData();

        for (const file of files) {
            const gcsUrl = await this.uploadToGCS(file.buffer, file.originalname);
            formData.append('files', file.buffer, file.originalname);
        }

        formData.append('job_description', jobDescription);
        formData.append('option', option);

        try {
            const response = await axios.default.post(
                `${process.env.AI_PYTHON_BASE_URL}evaluate`,
                formData,
                {
                    headers: {
                        ...formData.getHeaders(),
                    },
                },
            );

            const evaluationResult = response.data;
            
            if (!evaluationResult) {
                throw new Error('Evaluation result is empty.');
            }

            const result = evaluationResult.results[0];
            const { email_addresses, percentage_score, candidate_name, technical_role } = result;

            if (percentage_score >= 70) {
                
                const invitCandidate = await sendMail(
                    email_addresses,
                    this.mailerService,
                    'eRemoteHire',
                    candidate_name,
                    'atsInform',
                    {
                        name: candidate_name,
                        position: technical_role,
                        careerPageUrl: "https://perh-615475741690.us-central1.run.app/careers"
                    },
                )
            }

            return evaluationResult;
        } catch (error) {
            console.error('Error connecting to FastAPI:', error.message);
            throw new Error('Error connecting to FastAPI.');
        }
    }
}
