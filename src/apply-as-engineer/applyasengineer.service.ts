import * as AWS from 'aws-sdk';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';

import { AddEngineerDataDto } from './dto/addengineerData.dto';
import { Constants } from 'src/utils/constants';
import { DocumentService } from 'src/document/document.service';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { Injectable } from '@nestjs/common';
import { JWT, GoogleAuth } from 'google-auth-library';
import { PrismaService } from 'src/prisma.service';
import { ROOT_DIR } from '../../rootDir';
import { Storage } from '@google-cloud/storage';
import { UpdateEngineerDataDto } from './dto/updateengineerData.dto';
import axios from 'axios';

// const storage = new Storage();
@Injectable()
export class ApplyAsEngineerService {
  private readonly storage: Storage;
  private doc: GoogleSpreadsheet;
  constructor(
    private prisma: PrismaService,
    private documentService: DocumentService
  ) {
    const filePath = path.resolve(ROOT_DIR, "eremote-hire-website-cloud.json");
    const credentials = fs.readFileSync(filePath, 'utf8');

    this.storage = new Storage({
      projectId: process.env.PROJECT_ID,
      keyFilename: process.env.KEYFILENAME,
    });
    const bucketName = process.env.CLOUD_BUCKET_NAME;
    const auth = new GoogleAuth({
      credentials: JSON.parse(credentials),
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    this.doc = new GoogleSpreadsheet(process.env.GOOGLE_SPREADSHEET_ID, auth);
  }

  async addEngineerData(
    addEngineerDataDto: AddEngineerDataDto,
    updateEngineerDataDto: UpdateEngineerDataDto,
    files: Express.Multer.File[],
  ) {

    await this.doc.loadInfo();
    const sheet = this.doc.sheetsByIndex[0];
    const addDocument = await this.addDocuments(files);
    const addDocumentShortUrl = await this.shortenUrl(addDocument);
    const rowData = [
      addEngineerDataDto.name,
      addEngineerDataDto.emailId,
      addEngineerDataDto.phoneNo,
      addEngineerDataDto.linkedInUrl,
      addDocumentShortUrl,
    ];

    await sheet.addRow(rowData);
    const engineerData = await this.prisma.applyAsEngineer.create({
      data: {
        name: addEngineerDataDto.name,
        emailId: addEngineerDataDto.emailId,
        phoneNo: Number(addEngineerDataDto.phoneNo),
        linkedinUrl: addEngineerDataDto.linkedInUrl,
        resume: addDocumentShortUrl,
      },
    });

    return engineerData;
  }


  async shortenUrl(longUrl: string) {
    const apiUrl = `https://is.gd/create.php?format=json&url=${encodeURIComponent(longUrl)}`;

    try {
      const response = await axios.get(apiUrl);
      return response.data.shorturl;
    } catch (error) {
      console.error('Error shortening URL:', error);
      return longUrl; // Return the original URL if an error occurs
    }
  }
  // private async checkIfFileExists(s3: AWS.S3): Promise<boolean> {
  //   try {
  //     await s3
  //       .headObject({
  //         Bucket: Constants.bucketName,
  //         Key: 'applyAsEngineer.xlsx',
  //       })
  //       .promise();
  //     return true;
  //   } catch (error) {
  //     if (error.code === 'NotFound') {
  //       return false;
  //     }
  //     throw error;
  //   }
  // }

  // private async downloadFileFromS3(s3: AWS.S3): Promise<Buffer> {
  //   const params: AWS.S3.GetObjectRequest = {
  //     Bucket: Constants.bucketName,
  //     Key: 'applyAsEngineer.xlsx',
  //   };
  //   const response = await s3.getObject(params).promise();
  //   return response.Body as Buffer;
  // }

  private async checkIfFileExists(bucketName: string, fileName: string) {
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    return new Promise((resolve, reject) => {
      file.exists((err, exists) => {
        if (err) {
          reject(err);
        } else {
          resolve(exists);
        }
      });
    });
  }

  private async downloadFileFromGCS(bucketName: string, fileName: string): Promise<Buffer> {
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    return new Promise((resolve, reject) => {
      file.download((err, contents) => {
        if (err) {
          reject(err);
        } else {
          resolve(contents);
        }
      });
    });
  }

  async getEngineerData(id: string) {
    if (id) {
      const engineerData = await this.prisma.applyAsEngineer.findUnique({
        where: {
          id,
        },
      });
      return engineerData;
    } else {
      const engineerData = await this.prisma.applyAsEngineer.findMany({});
      return engineerData;
    }
  }

  async deleteEngineerData(id: string) {
    if (id) {
      const engineerData = await this.prisma.applyAsEngineer.delete({
        where: {
          id,
        },
      });
      return engineerData;
    } else {
      const engineerData = await this.prisma.applyAsEngineer.deleteMany({});
      return engineerData;
    }
  }

  async addDocuments(
    files: Express.Multer.File[],
    userId?: string,
    foldername?: string,
  ) {
    if (userId) {
      const folderName = foldername;
      const documentsUrls = await this.uploadFilesToGCS(files, folderName);

      const Documents = await this.prisma.document.create({
        data: {
          userId: userId,
          document: documentsUrls,
        },
      });
      return Documents.document[0];
    } else {
      const folderName = `${Constants.applyAsEngineer}/${Constants.resume}`;
      const documentsUrls = await this.uploadFilesToGCS(files, folderName);

      const Documents = await this.prisma.document.create({
        data: {
          // userId: userId,
          document: documentsUrls,
        },
      });
      return Documents.document[0];
    }
  }

  async uploadFilesToGCS(files: Express.Multer.File[], folderName: string) {
    const bucketName = 'eremotehire';
    const bucket = this.storage.bucket(bucketName);
    const documentsUrls = [];

    await Promise.all(
      files.map(async (file) => {
        const destination = folderName ? `${folderName}/${file.originalname}` : file.originalname;
        const fileUpload = bucket.file(destination);
        await fileUpload.save(file.buffer);
        const [url] = await fileUpload.getSignedUrl({ action: 'read', expires: '03-09-2491' });
        documentsUrls.push(url);
      })
    );

    return documentsUrls;
  }
}
