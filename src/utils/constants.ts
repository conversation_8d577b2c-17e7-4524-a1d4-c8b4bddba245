export const Constants = {
  user: 'user',
  updateUserSuccess: 'userData updated Successfully',
  creatUserSuccess: 'user<PERSON><PERSON> created Successfully',
  creatUserError: 'Unable to create userData',
  updateUserError: 'Unable to update userData',
  getUserSuccess: 'UserData get Successfully',
  getUserError: 'Unable to get User Data',
  deleteUserSuccess: 'user deleted Successfully',
  deleteUserError: 'Unable to delete UserData',
  files: 'files',
  addDocumentSuccess: 'Document added successfully',
  addDocumentError: 'Unable to Added Document',
  Documents: 'remotehire',
  bucketName: 'remotehiredocuments',
  userDocumentDetailsGetSuccess: 'Document data fetch successfully',
  userDocumentDetailsGetError: 'Unable to fetch document Data',
  deleteDocumentDataError: 'Unable to delete document Data',
  deleteDocumentDataSuccess: 'document data deleted successfully',
  DocumentKeywordGetSuccess: 'document keyword get successfully',
  DocumentKeywordGetError: 'Unable to match document keyword ',
  DocumentTranscribeGetSuccess: 'Transcribe text fetch success',
  DocumentTranscribeGetError: 'Unable to fetch transcribe text',
  signInSuccess: 'Successfully login',
  signInError: 'Unable to SignIn',
  emailId: 'emailId',
  invalid: 'Invalid User',
  userId: 'userId',
  error: 'Unauthorized',
  documentField: 'profilePicture',
  passwordChangeSuccess: 'Password Changed successfully',
  passwordChangeError: 'Unable to Change password',
  resume: 'resume',
  JWT: 'jwt',
  addHiredDataSuccess: 'Hired Data added Successfully',
  addHiredDataError: 'Unable to Added HiredData',
  getHiredDataSuccess: 'Hired Data Get Successfully',
  getHiredDataError: 'Unable to get HiredData',
  addRecommendationDataSuccess: 'RecommendationData added successfully',
  addRecommendationDataError: 'Unable to added recommendationData',
  getRecommendationDataSuccess: 'Recommendation Data Get Successfully',
  getRecommendationDataError: 'Unable to get Recommendation Data',
  deleteHiredDataSuccess: 'HiredData Deleted Successfully',
  deleteHiredDataError: 'Unable to delete Hired Data',
  deleteRecommendationDataSuccess: 'Successfully Deleted Recommendation Data',
  deleteRecommendationDataError: 'Unable to delete Recommendation Data',
  giveBonusDataSuccess: 'Bonus give Successfully',
  giveBonusDataError: 'Unable to give Bonus',
  giveRaiseDataSuccess: 'Raise given Successfully',
  giveRaiseDataError: 'Unable to give Raise',
  addEngineerDataSuccess: 'Engineer Data Added Successfully',
  addEngineerDataError: 'Unable to Add Engineer Data',
  getEngineerDataSuccess: 'Engineer Data Get Successfully',
  getEngineerDataError: 'Unable to get Engineer Data',
  deleteEngineerDataSuccess: 'Engineer Data deleted Successfully',
  deleteEngineerDataError: 'Unable to delete Engineer Data',
  addHireEngineerDataSuccess: 'Hire Data Added Successfully',
  addHireEngineerDataError: 'Unable to added Hire Data',
  getHireEngineerDataSuccess: 'HiredData Get successfully',
  deleteHireEngineerDataSuccess: 'Successfully deleted hiredData',
  getHireEngineerDataError: 'Unable to get Hired Data',
  deleteHireEngineerDataError: 'Unable to delete Hired Data',
  feedBackDataSuccess: 'Feedback data added successfully',
  feedBackDataError: 'Unable to added feedBack Data',
  feedBack: 'feedBack',
  clientProfile: 'clientProfile',
  developerProfile: 'developerProfile',
  getClientReviewDataSuccess: 'Client Review Data Get Successfully',
  getClientReviewDataError: 'Unable to get Client Data',
  addClientReviewDataSuccess: 'Client Data Added Successfully',
  addClientReviewDataError: 'Unable to add Client Data',
  addhireDeveloperDataSuccess: 'Available hire developer Added',
  addhireDeveloperDataError: 'Unable to add Hire developer',
  getAvailableHireDeveloperSuccess: 'Successfully get available hire developer',
  getAvailableHireDeveloperError: 'Unable to get available hire developer',
  addFounderDataSuccess: 'Founder Data Added Successfully',
  addFounderDataError: 'Unable to add FounderData',
  getFounderDataSuccess: 'Successfully get Founder Data',
  getFounderDataError: 'Unable to get Founder Data',
  addTeamDetailsDataSuccess: 'Team Details Added Successfully',
  addTeamDetailsDataError: 'Unable to Added Team Details',
  getTeamDetailsDataSuccess: 'Successfully get Team Details',
  getTeamDetailsDataError: 'Unable to get Team Member Details',
  addCommunityDetailsSuccess: 'Community Details Data added Success',
  addCommunityDetailsDataError: 'Unable to added community details',
  getCommunityDetailsDataSuccess: 'Successfully get Community Details Data',
  getCommunityDetailsDataError: 'Unable to get Community Details Data',
  communityProfile: 'communityProfile',
  addBenefitsDataSuccess: 'Benefits Data Added Successfull',
  addBenefitsDataError: 'Unable to added Benefits Data',
  getBenefitsListSuccess: 'Benefits Data get Successfully',
  getBenefitsListError: 'Unable to get Benefits Data',
  benefits: 'Benefits',
  getBenefitsListQuestionSuccess: "Benefit's Question Get Successfully",
  getBenefitsListQuestionError: 'Unable to get Benefits question',
  deleteBenefitsDataSuccess: 'Benefits Data deleted Successfully',
  deleteBenefitsDataError: 'Unable to delete Benefits Data',
  AddBenefitsQADataSuccess:
    'Benefits Question and answer Data stored successfully',
  AddBenefitsQADataError: 'Unable to store Question answer Data',
  getBenefitsQADataSuccess:
    'Benefits Questions and answer Data get Successfully',
  getBenefitsQADataError: 'Unable to get Question and answer Data',
  AddRemotePointsDataSuccess: 'RemotePoints Data Added Successfully',
  AddRemotePointsDataError: 'Unable to Added RemotePoints Data',
  updateRemotePointsDataSuccess: 'Remote points data updated Successfully',
  updateRemotePointsDataError: 'Unable to update Remote points Data',
  getRemotePointsHistoryDataSuccess: 'RemotePoints Data get successfully',
  getRemotePointsHistoryDataError: 'Unable to get RemotePoints Data',
  getTotalRemotePointsDataSuccess: 'Successfully get RemotePoints Data',
  getTotalRemotePointsDataError: 'Unable to get RemotePoints Data',
  addRequestForPointsSuccess: 'Points request added successfully',
  addRequestForPointsError: 'Unable to add point requested',
  getRequestForRedeemPointsSuccess:
    'successfully get redeem points request details',
  getRequestForRedeemPointsError: 'Unable to get redeem points Details',
  RedeemPointsRequestApproveSuccess:
    'redeem point request approved Successfully',
  RedeemPointsRequestApproveError: 'Unable to approved point redeem request',
  addCertificateSuccessfully: 'Certificate data added successfully',
  addCertificateError: 'Unable to added certificate data',
  getCertificateDataSuccess: 'Certificate data get successfully',
  getCertificateDataError: 'Unable to added certificate Data',
  addFAQDataSuccessfully: 'FAQ Data added successfully',
  addFAQDataError: 'Unable to added FAQ Data',
  getFAQDataSuccess: 'FAQ Data get successfully',
  getFAQDataError: 'Unable to get FAQ Data',
  emailSentSuccess: 'OTP sent on mail successfully',
  emailSentError: 'Unable to sent OTP on mail',
  otpValid: 'OTP Valid',
  fileTypeNotAllowed: 'File type not allowed',
  emailExist: 'EmailId is already exist',
  samePassword: 'Password Already Taken',
  passswordNotMatch: 'Password Not Match',
  emailNotExist: 'Email Not Exist',
  forgotPassword: 'Forgot Password',
  otpExpire: 'OTP Expire',
  otpInValid: 'OTP InValid',
  certificate: 'certificate',
  invalidPoints: 'Invalid Points',
  pageNameNotFound: 'Page Name Not Found',
  debited: 'debited',
  assignSuccessManagerSuccessfully: 'Manager assign successfully',
  assignSuccessManagerError: 'Unable to assign Success Manager',
  getSuccessManagerDataSuccessfully: 'Successfully get Manager Data',
  getSuccessManagerDataError: 'unable to get Manager Data',
  getDeveloperManagerDataSuccessfully: 'developer managerData get Successfully',
  getDeveloperManagerDataError: 'Unable to get developer manager Data',
  addPaymentDataSuccessfully: 'Payment data added successfully',
  addPaymentDataError: 'Unable to add payment Data',
  getPaymentDataSuccess: 'Payment Data get Successfully',
  getPaymentDataError: 'Unable to get Payment Data',
  paymentSlip: 'PaymentSlip',
  addWeeklyReportDataSuccessfully: 'Weekly Report Data Added Successfully',
  addWeeklyReportDataError: 'Unable to added weekly Report Data',
  getWeeklyReportDataSuccess: 'Weekly Report Data Get SuccessFully',
  getWeeklyReportDataError: 'Unable to get Weekly Report Data',
  getClientMonthlyReviewSuccess: 'Client Review Data Get Successfully',
  getClientMonthlyReviewError: 'Unable to get Client Review Data',
  addClientMonthlyReviewDataSuccess: 'Clinet Monthly data Added SUccessfully',
  addClientMonthlyReviewDataError: 'Unable to Added Client Monthly Data',
  profilePicture: 'profilePicture',
  applyAsEngineer: 'applyAsEngineer',
  addSuccessStoryDetailsSuccess: 'SuccessStory Details added Successfully',
  addSuccessStoryDetailsDataError: 'Unable to added SuccessStory Details',
  getSuccessStoryDetailsDataSuccess: 'SuccessStory details get successfully',
  getSuccessStoryDetailsDataError: 'Unable to get SuccessStory details',
  logoAddedSuccess: 'Logo Added Successfully',
  logoAddError: 'Unable to add Logo',
  logoGetSuccess: 'Successfully get logo data',
  logoGetError: 'Unable to get Logo Data',
  trustedBy: 'TrustedBy',
  developerAssignToClientSuccess: 'developer assign to client Successfully',
  developerAssignToClientError: 'Unable to assign developer to client',
  getBonusDataSuccess: 'Bonus Data Get Successfully',
  getBonusDataError: 'Unable to get Bonus Data',
  getRaiseDataSuccess: 'Raise Data Get Successfully',
  getRaiseDataError: 'Unable to get Raise Data',
  developerRaiseHistoryDataSuccess:
    'Successfully Get Developer Raise History Data',
  developerRaiseHistoryDataError: 'Unable to Get Developer Raise History Data',
  developerBonusHistoryDataSuccess: 'Successfully Get Bonus History Data',
  developerBonusHistoryDataError: 'Unable to Get Bonus History Data',
  totalBonusDataSuccess: 'Successfully get total bonus Given Data',
  totalBonusDataError: 'Unable to get total bonus Data',
  getMonthlyPaymentDataSuccess: 'successfully Get the Monthly payment data',
  getMonthlyPaymentDataError: 'Unable to Get the monthly payment data',
  getTotalMonthlyPaymentDataSuccess:
    'Successfully get the total monthly payment data',
  getTotalMonthlyPaymentDataError:
    'Unable to get the total monthly payment data',
  addVideoSuccess: 'Video added successfully',
  addVideoError: 'Unable to added video',
  getVideoDataSuccess: 'Video Data Get Successfully',
  getVideoDataError: 'Unable to get vidoe Data',
  deleteVideoDataSuccess: 'Video Data deleted Successfully',
  deleteVideoDataError: 'Unable to delete Video Data',
  videoURLExists: 'Video URL already exists',
  addWeeklySummariesDataSuccessfully:
    'weekly Summaries Data Added successfully',
  addWeeklySummariesDataError: 'Unable to add weeklySummariesData',
  getWeeklySummariesDataSuccess: 'Successfully get weeklySummariesData',
  getWeeklySummariesDataError: 'Unable to get weeklySummaries Data',
  addEremoteLabDataSuccess: 'EremoteLab data Added successfully',
  addEremoteLabDataError: 'Unable to added EremoteLabData',
  getEremoteLabDataSuccess: 'Successfully get eRemoteLabData',
  getEremoteLabDataError: 'Unable to get eRemoteLabData',
  getSearchTalentDataSuccess: 'Successfully Get the search talent data',
  getSearchTalentDataError: 'Unable to Get the search talent data',
  noMatchingRecourdsFound: 'No matching records found',
  filterAppliedSuccessfully: 'Filter applied successfully',
  errorApplyingFilter: 'Error applying filter',
  searchTalentSuccess: 'Successfully search talent',
  searchTalentError: 'Unable to search talent',
  requestInterviewSuccess: 'Request for interview sent successfully',
  requestInterviewError: 'Unable to send request for interview',
  addGptVettingDataSuccess: 'Gpt Vetting Data Added Successfully',
  addGptVettingDataError: 'Unable to added Gpt Vetting Data',
  getGptVettingDataSuccess: 'GptVetting Get successfully',
  getGptVettingDataError: 'Unable to get GptVetting Data',
  sendCandidateInviteSuccess: 'Candidate invite email sent successfully.',
  sendCandidateInviteError: 'Unable to send invite to cadidate.',
  questionsGeneratedSuccess: 'Questions Generated Successfully',
  questionsGeneratedError: 'Unable to generate questions',
};

export const endpoints = {
  user: '/user',
  updateUser: '/updateUser',
  getUserData: '/getUserData',
  deleteUserData: '/deleteUserData',
  addDocuments: '/addDocuments',
  fetchDocumentData: '/fetchDocumentData',
  deleteDocumentData: '/deleteDocumentData',
  login: '/login',
  forgotPassword: '/forgotPassword',
  addData: '/addData',
  applyAsEngineer: '/applyAsEngineer',
  getData: '/getData',
  deleteData: '/deleteData',
  addHireDataWeb:'/addHireDataWeb', 
  addHireData: '/addHireData',
  getHireData: '/getHireData',
  deleteHireData: '/deleteHireData',
  hiretopengineer: '/hiretopengineer',
  giveFeedback: '/giveFeedback',
  developer: '/developer',
  addClientReview: '/addClientReview',
  getClientReview: '/getClientReview',
  addAvailableHireDeveloper: '/addAvailableHireDeveloper',
  getAvailableHireDeveloper: '/getAvailableHireDeveloper',
  addFounder: '/addFounder',
  getFounder: '/getFounder',
  addTeamDetails: '/addTeamDetails',
  getTeamDetails: '/getTeamDetails',
  addCommunityDetails: '/addCommunityDetails',
  getCommunityDetails: '/getCommunityDetails',
  home: '/home',
  addBenefits: '/addBenefits',
  getBenefitsList: '/getBenefitsList',
  devdashboard: '/devdashboard',
  getBenefitsListQuestion: '/getBenefitsListQuestion',
  deleteBenefitsData: '/deleteBenefitsData',
  AddBenefitsQAData: '/AddBenefitsQAData',
  getBenefitsQAData: '/getBenefitsQAData',
  addRemotePoints: '/addRemotePoints',
  updateRemotePoints: '/updateRemotePoints',
  getRemotePointsHistory: '/getRemotePointsHistory',
  getTotalRemotePoints: '/getTotalRemotePoints',
  addRequestForPoints: '/addRequestForPoints',
  getRequestForRedeemPoints: '/getRequestForRedeemPoints',
  redeemPointsRequestApprove: '/redeemPointsRequestApprove',
  addCertificate: '/addCertificate',
  getCertificateData: '/getCertificateData',
  addFAQData: '/addFAQData',
  getFAQData: '/getFAQData',
  document: '/document',
  myteam: '/myteam',
  addUpdateHiredData: '/addUpdateHiredData',
  deleteHiredData: '/deleteHiredData',
  getHiredData: '/getHiredData',
  addUpdateRecommendationData: '/addUpdateRecommendationData',
  getRecommendationData: '/getRecommendationData',
  deleteRecommendationData: '/deleteRecommendationData',
  giveBonusData: '/giveBonusData',
  giveRaiseData: '/giveRaiseData',
  sendOTPforForgotPassword: '/sendOTPforForgotPassword',
  verifyOTP: '/verifyOTP',
  redeemPoints: '/redeemPoints',
  assignSuccessManager: '/assignSuccessManager',
  getSuccessManagerData: '/getSuccessManagerData',
  getdeveloperManagerData: '/getdeveloperManagerData',
  addPaymentData: '/addPaymentData',
  getPaymentData: '/getPaymentData',
  addWeeklyReportData: '/addWeeklyReportData',
  getWeeklyReportData: '/getWeeklyReportData',
  getClientMonthlyReview: '/getClientMonthlyReview',
  addClientMonthlyReview: '/addClientMonthlyReview',
  updateDocuments: '/updateDocuments',
  addSuccessStory: '/addSuccessStory',
  getSuccessStoryDetails: '/getSuccessStoryDetails',
  getTrustedByLogoData: '/getTrustedByLogoData',
  addTrustedByLogoData: '/addTrustedByLogoData',
  developerAssignToClient: '/developerAssignToClient',
  getBonusData: '/getBonusData',
  getRaiseData: '/getRaiseData',
  getRaiseHistoryData: '/getRaiseHistoryData',
  getBonusHistoryData: '/getBonusHistoryData',
  getTotalBonusData: '/getTotalBonusData',
  getClientPaymentData: '/getClientPaymentData',
  getMonthlyPaymentData: '/getMonthlyPaymentData',
  getTotalMonthlyPaymentData: '/getTotalMonthlyPaymentData',
  addVideo: '/addVideo',
  getVideo: '/getVideo',
  deleteVideo: '/deleteVideo',
  addWeeklySummariesData: '/addWeeklySummariesData',
  getWeeklySummariesData: '/getWeeklySummariesData',
  addERemoteLabData: '/addERemoteLabData',
  getERemoteLabData: '/getERemoteLabData',
  getSearchTalentData: '/getSearchTalentData',
  filterSearchTalent: '/filterSearchTalent',
  searchTalent: '/searchTalent',
  requestInterview: '/requestInterview',
  gptVetting: '/gptVetting',
  add: '/add',
  get: '/get',
  sendEmail: '/sendEmail',
  register: '/register',
  ats: '/ats',
  update: '/update',
  webhook: '/webhook',
  stripe: '/stripe',
  generateQuestions: '/generateQuestions',
  generateCodingQuestions: '/generateCodingQuestions',
  submit: '/submit',
};

export type PayloadType = {
  userId: string;
};
