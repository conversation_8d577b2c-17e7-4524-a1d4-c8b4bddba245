import { Test, TestingModule } from '@nestjs/testing';

import { GptVettingService } from './gptVetting.service';

describe('GptVettingService', () => {
  let service: GptVettingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GptVettingService],
    }).compile();

    service = module.get<GptVettingService>(GptVettingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
