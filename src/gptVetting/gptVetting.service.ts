import * as FormData from 'form-data';

import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { generateSignedUrl, sendMail, uploadFilesStreamToGCS } from 'src/utils/functions';

import { AddGptVettingDto } from './dto/addGptVetting.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { PassThrough } from 'stream';
import { PrismaService } from 'src/prisma.service';
import { Readable } from 'stream';
import { Storage } from '@google-cloud/storage';
import { UpdateGptVettingDto } from './dto/updateGptVetting.dto';
import axios from 'axios';
import { parse } from 'fast-csv';
import { parseString } from 'fast-csv';
import { timeout } from 'rxjs';
import { uploadFilesToGCS } from 'src/utils/functions';

const fs = require('fs');

@Injectable()
export class GptVettingService {
  private readonly storage: Storage;
  private readonly bucketName: string;
  public proctoringValue: boolean = false; // Default value for proctoring

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
  ) {
    this.storage = new Storage({
      projectId: process.env.PROJECT_ID,
      keyFilename: process.env.KEYFILENAME,
    }); // Initialize the Google Cloud Storage client
    this.bucketName = process.env.CLOUD_BUCKET_NAME || ''; // Ensure bucket name is set
  }

  async addGptVettingData(
    addGptVettingDto: AddGptVettingDto,
    files: Express.Multer.File[],
  ) {
    // Ensure the bucket is accessible
    const bucket = this.storage.bucket(this.bucketName);

    if (
      !files ||
      !files.find((file) => file.fieldname === 'answer[]') ||
      !files.find((file) => file.fieldname === 'videoFile')
    ) {
      throw new Error('Files are missing');
    }

    const {
      userId,
      name,
      testTaken,
      dateTaken,
      techStack,
      softSkills,
      proctoringResult,
      success,
      question,
      skillRating,
      feedback,
      task,
      code,
      email,
      codeFeedback,
      frameCount,
      lookingAwayCount,
      multipleFacesCount,
      noFaceDetectedCount,
      tabSwitchCount,
    } = addGptVettingDto;

    const parsedProctoringResult = parseFloat(
      proctoringResult as unknown as string,
    );
    const parsedSuccess =
      typeof success === 'string' ? success === 'true' : success;

    // Parse new fields as float
    const parsedFrameCount = parseFloat(frameCount as unknown as string);
    const parsedLookingAwayCount = parseFloat(
      lookingAwayCount as unknown as string,
    );
    const parsedMultipleFacesCount = parseFloat(
      multipleFacesCount as unknown as string,
    );
    const parsedNoFaceDetectedCount = parseFloat(
      noFaceDetectedCount as unknown as string,
    );
    const parsedTabSwitchCount = parseFloat(tabSwitchCount as unknown as string);

    const folderName = 'gptVetting';

    const audioFiles = files.filter((file) => file.fieldname === 'answer[]');
    const videoFile = files.find((file) => file.fieldname === 'videoFile');

    // Upload audio files
    const audioUrls = await Promise.all(
      audioFiles.map((file) =>
        uploadFilesStreamToGCS(file, folderName, this.storage, this.bucketName),
      ),
    );

    // Upload video file
    let videoUrl: string | undefined;
    if (videoFile) {
      const videoUpload = await uploadFilesStreamToGCS(
        videoFile,
        folderName,
        this.storage,
        this.bucketName,
      );
      videoUrl = Array.isArray(videoUpload) ? videoUpload[0] : videoUpload;
    }

    return this.prisma.gptVetting.create({
      data: {
        userId,
        name,
        testTaken,
        dateTaken,
        techStack,
        softSkills,
        proctoringResult: parsedProctoringResult,
        success: parsedSuccess,
        question,
        answer: audioUrls.flat(),
        video: videoUrl,
        skillRating,
        feedback,
        task,
        code,
        email,
        codeFeedback,
        frameCount: parsedFrameCount,
        lookingAwayCount: parsedLookingAwayCount,
        multipleFacesCount: parsedMultipleFacesCount,
        noFaceDetectedCount: parsedNoFaceDetectedCount,
        tabSwitchCount: parsedTabSwitchCount,
      },
    });
  }

  async getGptVettingData(id?: string) {
    try {
      if (id) {
        const gptVettingData = await this.prisma.gptVetting.findUnique({
          where: { id },
        });

        if (!gptVettingData) {
          throw new NotFoundException('GPT Vetting data not found for this ID');
        }

        return gptVettingData;
      }

      return await this.prisma.gptVetting.findMany({
        orderBy: { 
          updatedAt: 'desc',
        }, 
      });
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Unable to fetch GPT Vetting Data',
      );
    }
  }

  async sendEmailToCandidate(
    emailId: string, 
    name: string, 
    skills?: { skill: string; level: string }[],
    isProctoring?: boolean,
    isCodingExcercise?: boolean,
  ) {
    try {
      const gptUser = await this.prisma.gptVetting.create({
        data: {
          email: emailId,
          name: name,
          techStack: skills?.length ? skills.map((s) => s.skill) : [],
          techLevel: skills?.length ? skills.map((s) => s.level) : [],
          isProctoring: isProctoring ?? false,
          isCodingExcercise: isCodingExcercise ?? false,
        },
      });

      try {
        const inviteCandidate = await sendMail(
          emailId,
          this.mailerService,
          'eRemoteHire',
          name,
          'candidateInvite',
          { vettingUrl: process.env.START_VETTING_URL, id: gptUser.id },
        );

        return inviteCandidate;
      } catch (emailError) {
        await this.prisma.gptVetting.delete({
          where: { id: gptUser.id },
        });

        throw new Error('Unable to send invite email to candidate');
      }
    } catch (error) {
      throw new Error('Unable to process candidate invitation');
    }
  }

  async updateGptVettingData(
    updateGptVettingDto: UpdateGptVettingDto,
    id: string,
  ) {
    try {
  
      const existingData = await this.prisma.gptVetting.findUnique({
        where: { id },
      });
  
      if (!existingData) {
        throw new NotFoundException('Gpt Vetting data not found for this id');
      }
      this.proctoringValue = existingData.isProctoring;
      const {
        proctoringResult,
        success,
        frameCount,
        lookingAwayCount,
        multipleFacesCount,
        noFaceDetectedCount,
        tabSwitchCount,
        question,
        answer,
        ...restDto
      } = updateGptVettingDto;

      if (typeof restDto.techStack === 'string') {
        try {
          restDto.techStack = JSON.parse(restDto.techStack);
        } catch (err) {
          throw new BadRequestException('Invalid format for techStack');
        }
      }

      const parsedProctoringResult =
        proctoringResult !== undefined
          ? parseFloat(proctoringResult as unknown as string)
          : undefined;
  
      const parsedSuccess =
        typeof success === 'string' ? success === 'true' : success;
  
      const parsedFrameCount =
        frameCount !== undefined
          ? parseFloat(frameCount as unknown as string)
          : undefined;
  
      const parsedLookingAwayCount =
        lookingAwayCount !== undefined
          ? parseFloat(lookingAwayCount as unknown as string)
          : undefined;
  
      const parsedMultipleFacesCount =
        multipleFacesCount !== undefined
          ? parseFloat(multipleFacesCount as unknown as string)
          : undefined;
  
      const parsedNoFaceDetectedCount =
        noFaceDetectedCount !== undefined
          ? parseFloat(noFaceDetectedCount as unknown as string)
          : undefined;
  
      const parsedTabSwitchCount =
        tabSwitchCount !== undefined
          ? parseFloat(tabSwitchCount as unknown as string)
          : undefined;
  
// const updatedData = await this.prisma.gptVetting.update({
      //   where: { id },
      //   data: {
      //     ...restDto,
      //     proctoringResult: parsedProctoringResult,
      //     success: parsedSuccess,
      //     frameCount: parsedFrameCount,
      //     lookingAwayCount: parsedLookingAwayCount,
      //     multipleFacesCount: parsedMultipleFacesCount,
      //     noFaceDetectedCount: parsedNoFaceDetectedCount,
      //     tabSwitchCount: parsedTabSwitchCount,
      //     question: Array.isArray(updateGptVettingDto.question)
      //     ? updateGptVettingDto.question.filter(q => q !== undefined && q !== null)
      //     : updateGptVettingDto.question
      //     ? [updateGptVettingDto.question]
      //     : [],
      //   answer: Array.isArray(updateGptVettingDto.answer)
      //     ? updateGptVettingDto.answer.filter(a => a !== undefined && a !== null)
      //     : updateGptVettingDto.answer
      //     ? [updateGptVettingDto.answer]
      //     : [],
      //     video: updateGptVettingDto.video || null,
      //     updatedAt: new Date(),
      //   },
      // });

      const updatedData = await this.prisma.gptVetting.update({
        where: { id },
        data: {
          name: restDto.name ?? existingData.name,
          testTaken: restDto.testTaken ?? existingData.testTaken,
          dateTaken: restDto.dateTaken ?? existingData.dateTaken,
          techStack: restDto.techStack ?? existingData.techStack,
          softSkills: restDto.softSkills ?? existingData.softSkills,
          proctoringResult: parsedProctoringResult ?? existingData.proctoringResult,
          success: parsedSuccess ?? existingData.success,
          skillRating: restDto.skillRating ?? existingData.skillRating,
          feedback: restDto.feedback ?? existingData.feedback,
          task: restDto.task ?? existingData.task,
          code: restDto.code ?? existingData.code,
          codeFeedback: restDto.codeFeedback ?? existingData.codeFeedback,
          email: restDto.email ?? existingData.email,
          frameCount: parsedFrameCount ?? existingData.frameCount,
          lookingAwayCount: parsedLookingAwayCount ?? existingData.lookingAwayCount,
          multipleFacesCount: parsedMultipleFacesCount ?? existingData.multipleFacesCount,
          noFaceDetectedCount: parsedNoFaceDetectedCount ?? existingData.noFaceDetectedCount,
          tabSwitchCount: parsedTabSwitchCount ?? existingData.tabSwitchCount,
  
          question: Array.isArray(question)
            ? question.filter((q) => q !== undefined && q !== null)
            : question
            ? [question]
            : existingData.question,
  
          answer: Array.isArray(answer)
            ? answer.filter((a) => a !== undefined && a !== null)
            : answer
            ? [answer]
            : existingData.answer,
  
          video: restDto.video ?? existingData.video,
          updatedAt: new Date(),
        },
      });

      // 🔁 Trigger background update (don't wait for it)
      this.submitInterview(updatedData)
      .then(async (aiResponse) => {
        console.log('AI enrichment response:', aiResponse);
        console.log('AI enrichment response:', updatedData);
        if (aiResponse) {
          await this.prisma.gptVetting.update({
            where: { id },
            data: {
              skillRating: aiResponse.skill_rating,
              proctoringResult: aiResponse.proctoring_score,
              codeFeedback: aiResponse.code_feedback,
              feedback: aiResponse.feedback,
              noFaceDetectedCount: aiResponse.no_face_detected_count,
              lookingAwayCount: aiResponse.looking_away_count,
              tabSwitchCount: aiResponse.tab_switch_count,
              dateTaken: new Date().toISOString().split("T")[0],
              success: true
            },
          });
        }
      })
      .catch((err) => {
        console.error('AI enrichment failed:', err);
        // optionally log or report this error somewhere
      });
console.log('Updated data before return:', updatedData);
      return updatedData;
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }

  async generateQuestions(body: { skill: string; level: string }[], numQuestions: number, isCodingExcercise?: boolean) {
    try {
      // let skills = JSON.stringify(body);
      
      // Fetch regular questions
      const questionsResponse = await axios.post(
        `${process.env.API_PYTHON_BASE_URL}interviews/generate-questions?num_questions=${numQuestions}`,
        body,
        { headers: { 'Content-Type': 'application/json' } },
      );

      // Fetch coding questions
      // const codingQuestionsResponse = await axios.post(
      //   `${process.env.API_PYTHON_BASE_URL}interviews/generate-coding-question?num_questions=1&generate_coding=${isCodingExcercise}`,
      //   body,
      //   { headers: { 'Content-Type': 'application/json' } },
      // );

      let codingQuestions = '';

      if (isCodingExcercise) {
        console.log('Generating coding questions...');
        const codingQuestionsResponse = await axios.post(
          `${process.env.API_PYTHON_BASE_URL}interviews/generate-coding-question?num_questions=1&generate_coding=true`,
          body,
          { headers: { 'Content-Type': 'application/json' } },
        );
        codingQuestions = codingQuestionsResponse.data.questions;
      }

      return {
        questions: questionsResponse.data.questions,
        // codingQuestions: codingQuestionsResponse.data.questions,
        codingQuestions,
      };
    } catch (error) {
      throw new Error(error.response?.data || 'Error fetching questions');
    }
  }

  // async submitInterview(interviewData: any): Promise<any> {
  //   try {
  //     const formData = new FormData();
      
  //     const videoStream = Readable.from(interviewData.videoFile.buffer);
  //     formData.append('video_file', videoStream, {
  //       filename: interviewData.videoFile.originalname,
  //       contentType: interviewData.videoFile.mimetype,
  //     });
  
  //     interviewData.audioFiles.forEach((file: any, index: number) => {
  //       const audioStream = Readable.from(file.buffer);
  //       formData.append("audio_files", audioStream, {
  //         filename: `audio_${index}.wav`,
  //         contentType: file.mimetype || "audio/wav",
  //       });
  //     });
      
  //     interviewData.questions.forEach((question: string) => {
  //       formData.append("questions", question);
  //     });
  
  //     formData.append("code_question", interviewData.codeQuestion);
  //     formData.append("user_id", interviewData.userId);
  //     formData.append("code", interviewData.code);
  //     formData.append("tabSwitchCount", interviewData.tabSwitchCount);
      
  //     const config = {
  //       method: 'post',
  //       maxBodyLength: Infinity,
  //       url: `${process.env.API_PYTHON_BASE_URL}interviews/submit`,
  //       headers: { 
  //         ...formData.getHeaders()
  //       },
  //       data: formData
  //     };
  
  //     const response = await axios.request(config);
  //     return response.data;
  //   } catch (error: any) {
  //     console.error("Error Response:", error?.response?.data || error.message);
  //     throw new Error("Failed to submit interview data");
  //   }
  // }

  // async submitInterview(interviewData: any): Promise<any> {
  //   try {
  //     const formData = new FormData();
  //     console.log(interviewData, " interviewData");
  
  //     // Append `questions` to formData
  //     // if (Array.isArray(interviewData.questions)) {
  //     //   interviewData.questions.forEach((question: string) => {
  //     //     formData.append("questions", question);
  //     //   });
  //     // }
  
  //     // // Fix: Change `asnwers` to `answers` & append to `audio_files`
  //     // if (Array.isArray(interviewData.answers)) { // <--- Change `asnwers` to `answers`
  //     //   interviewData.answers.forEach((answer: string) => {  
  //     //     formData.append("audio_urls", answer);
  //     //   });
  //     // }

  //     interviewData.questions.forEach((question: string) => {
  //       formData.append("questions[]", question);
  //     });

  //     interviewData.answers.forEach((answer: string) => {
  //       formData.append("audio_urls[]", answer);
  //     });


  //     formData.append("code_question", interviewData.codeQuestion || "");
  //     formData.append("user_id", interviewData.userId || "");
  //     formData.append("code", interviewData.code || "");
  //     formData.append("tabSwitchCount", interviewData.tabSwitchCount || "0");
  //     formData.append("video_url", interviewData.video || "");
  
  //     console.log(formData, " formData");
  
  //     const config = {
  //       method: 'post',
  //       maxBodyLength: Infinity,
  //       url: `${process.env.API_PYTHON_BASE_URL}interviews/submit`,
  //       headers: { 
  //         ...formData.getHeaders(),
  //       },
  //       data: formData
  //     };
  
  //     const response = await axios.request(config);
  //     return response.data;

  //   } catch (error: any) {
  //     console.error("Error Response:", error?.response?.data || error.message);
  //     throw new Error("Failed to submit interview data");
  //   }
  // }

  async submitInterview(interviewData: any) {
    const formData = new FormData();

    formData.append('user_id', "RH0007");
    formData.append('code_question', interviewData.task || '');
    formData.append('code', interviewData.code || '');
    if (interviewData.video) formData.append('video_url', interviewData.video);
    if (interviewData.tabSwitchCount) {
      formData.append('tabSwitchCount', parseInt(interviewData.tabSwitchCount));
    } else {
      formData.append('tabSwitchCount', 0);
    }
    // formData.append('tabSwitchCount', 3); 
    
    if (Array.isArray(interviewData.question)) {
        interviewData.question.forEach((questions) => {
            formData.append('questions', questions);
        });
    } else if (interviewData.question) {
        formData.append('questions', interviewData.question);
    }

    if (Array.isArray(interviewData.answer)) {
        interviewData.answer.forEach((url) => {
            formData.append('audio_urls', url);
        });
    } else if (interviewData.answer) {
        formData.append('audio_urls', interviewData.answer);
    }
    formData.append('proctoring', this.proctoringValue || false);
    
    try {
      const config = {
              method: 'post',
              maxBodyLength: Infinity,
              url: `${process.env.API_PYTHON_BASE_URL}interviews/submit`,
              headers: { 
                ...formData.getHeaders(),
                timeout: 30 * 60 * 1000, // 30 minutes in milliseconds
              },
              data: formData
            };
        
            const response = await axios.request(config);
            return response.data;
    } catch (error) {
        console.error('API Error:', error.response?.data || error.message);
        throw new BadRequestException(error.response?.data || 'Failed to submit interview data');
    }
  }

  async handleCSVUpload(buffer: Buffer): Promise<any> {
    const candidates: { name: string; email: string }[] = [];

    return new Promise((resolve, reject) => {
      const stream = Readable.from(buffer.toString());

      stream
        .pipe(parse({ headers: true }))
        .on('error', (error) => {
          reject(new HttpException(error.message, HttpStatus.BAD_REQUEST));
        })
        .on('data', (row) => {
          const { Name, Email } = row;
          if (Name && Email) {
            candidates.push({ name: Name.trim(), email: Email.trim() });
          }
        })
        .on('end', async () => {
          const results = [];

          for (const candidate of candidates) {
            try {
              const result = await this.sendEmailToCandidate(
                candidate.email,
                candidate.name,
              );
              results.push({ ...candidate, status: 'success' });
            } catch (err) {
              results.push({ ...candidate, status: 'failed', error: err.message });
            }
          }

          resolve({
            message: 'CSV processing completed',
            resultSummary: results,
          });
        });
    });
  }
}