import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  ParseArrayPipe,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { Constants, endpoints } from 'src/utils/constants';
import { GptVettingService } from './gptVetting.service';
import { AddGptVettingDto } from './dto/addGptVetting.dto';
import { Response } from 'express';
import { UpdateGptVettingDto } from './dto/updateGptVetting.dto';
import { ApiQuery } from '@nestjs/swagger';
import {
  AnyFilesInterceptor,
  FileInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import { GetSignedUrlConfig, Storage } from '@google-cloud/storage';
import { memoryStorage } from 'multer';

@Controller(endpoints.gptVetting)
export class GptVettingController {
  private readonly storage: Storage;
  private readonly bucketName: string;

  constructor(private gptVettingService: GptVettingService) {
    this.storage = new Storage({
      projectId: process.env.PROJECT_ID,
      keyFilename: process.env.KEYFILENAME,
    });
    this.bucketName = 'eremotehire';
  }

  @Post(endpoints.add)
  @UseInterceptors(AnyFilesInterceptor()) // Accepts multiple fields
  async addGptVettingData(
    @Res() response: Response,
    @Body() addGptVettingDto: AddGptVettingDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    try {
      const gptVettingData = await this.gptVettingService.addGptVettingData(
        addGptVettingDto,
        files,
      );
      return response.status(HttpStatus.CREATED).json({
        message: Constants.addGptVettingDataSuccess,
        gptVettingData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.addGptVettingDataError,
        error: err.message,
      });
    }
  }

  @Get(endpoints.get)
  @ApiQuery({ name: 'id', required: false })
  async getGptVettingData(@Res() response: Response, @Query('id') id: string) {
    try {
      const gptVettingData = await this.gptVettingService.getGptVettingData(id);
      return response.status(HttpStatus.CREATED).json({
        message: Constants.getGptVettingDataSuccess,
        gptVettingData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.getGptVettingDataError,
        error: err.message,
      });
    }
  }

  @Post(endpoints.sendEmail)
  async sendEmail(@Res() response: Response, @Body() body?: any) {
    try {
      
      const { emailId, name, skills = [], isProctoring, isCodingExcercise } = body;

      const sendInviteCandidate =
        await this.gptVettingService.sendEmailToCandidate(
          body.emailId,
          body.name,
          skills,
          isProctoring,
          isCodingExcercise
        );
      return response.status(HttpStatus.CREATED).json({
        message: Constants.sendCandidateInviteSuccess,
        sendInviteCandidate,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.sendCandidateInviteError,
        error: err.message,
      });
    }
  }

  // @Post(endpoints.update)
  // @UseInterceptors(AnyFilesInterceptor())
  // @ApiQuery({ name: 'id', required: true })
  // async updateGptVettingData(
  //   @Res() response: Response,
  //   @Body() updateGptVettingDto: UpdateGptVettingDto,
  //   @Query('id') id: string,
  //   @UploadedFiles() files: Express.Multer.File[],
  // ) {
  //   try {
  //     const gptVettingData = await this.gptVettingService.updateGptVettingData(
  //       updateGptVettingDto,
  //       id,
  //       files,
  //     );
  //     return response.status(HttpStatus.CREATED).json({
  //       message: Constants.addGptVettingDataSuccess,
  //       gptVettingData,
  //     });
  //   } catch (err) {
  //     return response.status(HttpStatus.BAD_REQUEST).json({
  //       message: Constants.addGptVettingDataError,
  //       error: err.message,
  //     });
  //   }
  // }
  
  @Post(endpoints.update)
  @UseInterceptors(FileInterceptor('')) 
  @ApiQuery({ name: 'id', required: true })
  async updateGptVettingData(
    @Res() response: Response,
    @Body() updateGptVettingDto: UpdateGptVettingDto,
    @Query('id') id: string,
  ) {
    try {
      const gptVettingData = await this.gptVettingService.updateGptVettingData(
        updateGptVettingDto,
        id
      );
      return response.status(HttpStatus.CREATED).json({
        message: Constants.addGptVettingDataSuccess,
        gptVettingData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.addGptVettingDataError,
        error: err.message,
      });
    }
  }

  @Post(endpoints.generateQuestions)
  @ApiQuery({ name: 'num_questions', required: true, type: Number })
  @ApiQuery({ name: 'is_coding_excercise', required: false, type: Boolean })
  async generateQuestions(
    @Res() response: Response,
    // @Body() body: string[],
    @Body() body: { skill: string; level: string }[], 
    @Query('num_questions') numQuestions: string,
    @Query('is_coding_excercise') isCodingExcercise?: boolean,
  ) {
    try {
      const parsedNumQuestions = parseInt(numQuestions, 10);
      if (isNaN(parsedNumQuestions) || parsedNumQuestions <= 0) {
        return response.status(HttpStatus.BAD_REQUEST).json({
          message: 'Invalid num_questions. It must be a positive number.',
        });
      }

      const { questions, codingQuestions } =
        await this.gptVettingService.generateQuestions(
          body,
          parsedNumQuestions,
          isCodingExcercise
        );

      return response.status(HttpStatus.CREATED).json({
        message: Constants.questionsGeneratedSuccess,
        questions,
        codingQuestions,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.questionsGeneratedError,
        error: err.message,
      });
    }
  }

  // @Post(endpoints.submit)
  // @UseInterceptors(AnyFilesInterceptor())
  // async submitInterview(
  //   @Body('user_id') userId: string,
  //   @Body('questions') questions: string,
  //   @Body('code_question') codeQuestion: string,
  //   @Body('code') code: string,
  //   @Body('tabSwitchCount') tabSwitchCount: number,
  //   @Res() response: Response,
  //   @UploadedFiles() files: Express.Multer.File[],
  // ) {
  //   try {
  //     const parsedQuestions = Array.isArray(questions)
  //       ? questions
  //       : [questions];
  //     const audioFiles = files.filter(
  //       (file) => file.fieldname === 'audio_files',
  //     );
  //     const videoFile = files.find((file) => file.fieldname === 'video_file');

  //     const interviewData = {
  //       userId,
  //       questions: parsedQuestions,
  //       codeQuestion,
  //       code,
  //       audioFiles,
  //       videoFile,
  //       tabSwitchCount
  //     };

  //     const savedInterview =
  //       await this.gptVettingService.submitInterview(interviewData);

  //     return response.status(HttpStatus.CREATED).json({
  //       message: 'Interview data submitted successfully',
  //       interviewData: savedInterview,
  //     });
  //   } catch (err) {
  //     console.log('Error:', err);
  //     return response.status(HttpStatus.BAD_REQUEST).json({
  //       message: 'Error submitting interview data',
  //       error: err.message,
  //     });
  //   }
  // }

  @Post(endpoints.submit)
  @UseInterceptors(FileInterceptor(''))
  async submitInterview(
    @Body('user_id') userId: string,
    @Body('questions') questions: string | string[],
    @Body('audio_urls') answers: string | string[],
    @Body('code_question') codeQuestion: string,
    @Body('code') code: string,
    @Body('tabSwitchCount') tabSwitchCount: number,
    @Body('video_url') video: string,
    @Res() response: Response,
  ) {
    try {
      const parsedQuestions = Array.isArray(questions) ? questions : questions ? [questions] : [];
      const parsedAnswers = Array.isArray(answers) ? answers : answers ? [answers] : [];

      const interviewData = {
        userId,
        questions : parsedQuestions,
        answers : parsedAnswers,
        codeQuestion: codeQuestion ?? '',
        code: code ?? '',
        tabSwitchCount: tabSwitchCount ?? 0,
        video: video ?? '',
      };

      const savedInterview = await this.gptVettingService.submitInterview(interviewData);

      return response.status(HttpStatus.CREATED).json({
        message: 'Interview data submitted successfully',
        interviewData: savedInterview,
      });
    } catch (err) {
      console.log('Error:', err);
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: 'Error submitting interview data',
        error: err.message,
      });
    }
  }

  @Get('/signed-url')
  async getSignedUrl(
    @Query('filename') filename: string,
    @Query('contentType') contentType: string,
    @Query('id') id: string,
    @Res() res: Response,
  ) {
    if (!filename || !contentType || !id) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Filename and content type and id are required.',
      });
    }

    try {
      const file = this.storage.bucket(this.bucketName).file(`gptVetting/${id}/${filename}`);
      const options: GetSignedUrlConfig = {
        version: 'v4',
        action: 'write',
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000,
        contentType,
      };

      const signedUrlResponse = await file.getSignedUrl(options);
      const url = signedUrlResponse[0];
      return res.status(HttpStatus.OK).json({ signedUrl: url });
    } catch (error) {
      console.error('Error generating signed URL:', error.message);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to generate signed URL.',
        details: error.message,
      });
    }
  }

  @Post('/uploadCSVCandidate')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.csv$/)) {
          return cb(new Error('Only CSV files are allowed!'), false);
        }
        cb(null, true);
      },
    }),
  )
  async uploadCSV(@UploadedFile() file: Express.Multer.File, @Res() response: Response,) {
    try {

    if (!file) {
      throw new HttpException('CSV file is required', HttpStatus.BAD_REQUEST);
    }

    const inviteCSVData = await this.gptVettingService.handleCSVUpload(file.buffer);
    
    return response.status(HttpStatus.CREATED).json({
      message: 'Invite CSV candidates successfully',
      interviewData: inviteCSVData,
    });
  } catch (err) {
    return response.status(HttpStatus.BAD_REQUEST).json({
      message: 'Error uploading CSV file',
      error: err.message,
    });
  }
  }
}
