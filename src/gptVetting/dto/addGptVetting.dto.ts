import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class AddGptVettingDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  testTaken?: string;

  @ApiProperty({
    description: 'Date in the format YYYY-MM-DD',
    example: '2025-01-01',
  })
  @IsOptional()
  @IsDateString()
  dateTaken?: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  techStack?: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  softSkills?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  proctoringResult?: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  success?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  skillRating?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  feedback?: string;
  
  @ApiProperty()
  @IsOptional()
  @IsString()
  task?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  question?: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  codeFeedback?: string;
  
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  frameCount?: number;
  
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  lookingAwayCount?: number;
  
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  multipleFacesCount?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  noFaceDetectedCount?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  tabSwitchCount?: number;
  
  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  techLevel?: string[];

  @ApiProperty()
  @IsOptional()
  @Transform(({value}) => value === "true")
  @IsBoolean()
  isProctoring?: boolean;

  @ApiProperty()
  @IsOptional()
  @Transform(({value}) => value === "true")
  @IsBoolean()
  isCodingExcercise?: boolean;
}
