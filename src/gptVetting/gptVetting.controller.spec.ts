import { Test, TestingModule } from '@nestjs/testing';

import { GptVettingController } from './gptVetting.controller';

describe('GptVettingController', () => {
  let controller: GptVettingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GptVettingController],
    }).compile();

    controller = module.get<GptVettingController>(GptVettingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
