import { GiveBonusDto } from './dto/giveBonus.Dto';
import { GiveRaiseDto } from './dto/giveRaise.Dto';
import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class MyteamService {
  constructor(private prisma: PrismaService) {}

  async getHiredData(userId: string) {
    const hiredData = [];
    const userData = await this.prisma.user.findMany({
      where: {
        client: userId,
      },
    });

    for (const user of userData) {
      const hourlyRate = user.hourlyRate || 0;
      const workingHoursPerDay = 8;
      const workingDaysPerMonth = 22;
      const monthlyPayment =
        hourlyRate * workingHoursPerDay * workingDaysPerMonth;

      const bonusData = await this.prisma.giveBonus.findMany({
        where: {
          userId: user.userId,
        },
      });

      let totalBonus = 0;
      bonusData.forEach((entry) => {
        totalBonus += entry.bonusAmount;
      });

      const raiseData = await this.prisma.giveRaise.findMany({
        where: {
          userId: user.userId,
        },
      });

      const vettingResultData = await this.prisma.vettingResults.findMany({
        where: {
          userId: user.userId,
        },
      });

      const weeklySummaries = await this.prisma.weeklySummaries.findMany({
        where: {
          userId: user.userId,
        },
      });

      const educationDetails = await this.prisma.educationDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const experienceDetails = await this.prisma.experienceDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      hiredData.push({
        userData: user,
        monthlySalary: monthlyPayment,
        bonusHistory: bonusData,
        raiseHistory: raiseData,
        totalBonusGiven: totalBonus,
        vettingResult: vettingResultData,
        weeklySummaries: weeklySummaries,
        educationDetails: educationDetails,
        experienceDetails: experienceDetails,
      });
    }
    return hiredData;
  }

  async getRecommendationData() {
    
    const hireForm = await this.prisma.hireTopEngineer.findMany();
    const lasthireForm = hireForm[hireForm.length - 1];
    
    const selectedSkills = lasthireForm?.skill || [];

    const userData = await this.prisma.user.findMany({
      where: {
        currentStatus: 'openToWork',
      },
    });
    const recommand: any[]=[];
    const others: any[]=[];

    for (const user of userData) {
      const userTechStack = user.techStack || [];
      const isMatch = userTechStack.some((skill: string) =>
        selectedSkills.includes(skill)
      );
      const hourlyRate = user.hourlyRate || 0;
      const workingHoursPerDay = 8;
      const workingDaysPerMonth = 22;
      const monthlyPayment =
        hourlyRate * workingHoursPerDay * workingDaysPerMonth;

      const vettingResultData = await this.prisma.vettingResults.findMany({
        where: {
          userId: user.userId,
        },
      });

      const educationDetails = await this.prisma.educationDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const experienceDetails = await this.prisma.experienceDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const userObject = {
        userData: user,
        monthlyPayment: monthlyPayment,
        vettingResults: vettingResultData,
        educationDetails: educationDetails,
        experienceDetails: experienceDetails,
      };

      if (isMatch) {
        recommand.push(userObject);
      } else {
        others.push(userObject);
      }
    }
    return{recommand, others};
  }

  async giveBonusData(giveBonusDto: GiveBonusDto, userId: string) {
    const bonusData = await this.prisma.giveBonus.create({
      data: {
        userId: userId,
        bonusAmount: Number(giveBonusDto.bonusAmount),
        date: new Date(),
      },
    });
    return bonusData;
  }

  async giveRaiseData(giveRaiseDto: GiveRaiseDto, userId: string) {
    const raiseData = await this.prisma.giveRaise.create({
      data: {
        userId: userId,
        raiseAmount: giveRaiseDto.raiseAmount,
        effectiveOn: new Date(giveRaiseDto.effectiveOn),
        currentRate: giveRaiseDto.currentRate,
        afterRaiseRate: giveRaiseDto.afterRaiseRate,
        messageRegardingRaise: giveRaiseDto.messageRegardingRaise,
        sentOnDate: new Date(),
      },
    });
    return raiseData;
  }

  async getBonusData(userId: string) {
    if (userId) {
      const bonusData = await this.prisma.giveBonus.findMany({
        where: {
          userId: userId,
        },
      });
      return bonusData;
    }
  }

  async getRaiseData(userId: string) {
    if (userId) {
      const raiseData = await this.prisma.giveRaise.findMany({
        where: {
          userId: userId,
        },
      });
      return raiseData;
    }
  }
}
