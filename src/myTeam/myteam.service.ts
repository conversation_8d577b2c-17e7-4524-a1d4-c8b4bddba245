import { GiveBonusDto } from './dto/giveBonus.Dto';
import { GiveRaiseDto } from './dto/giveRaise.Dto';
import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class MyteamService {
  constructor(private prisma: PrismaService) {}

  async getHiredData(userId: string) {
    const hiredData = [];
    const userData = await this.prisma.user.findMany({
      where: {
        client: userId,
      },
    });

    for (const user of userData) {
      const hourlyRate = user.hourlyRate || 0;
      const workingHoursPerDay = 8;
      const workingDaysPerMonth = 22;
      const monthlyPayment =
        hourlyRate * workingHoursPerDay * workingDaysPerMonth;

      const bonusData = await this.prisma.giveBonus.findMany({
        where: {
          userId: user.userId,
        },
      });

      let totalBonus = 0;
      bonusData.forEach((entry) => {
        totalBonus += entry.bonusAmount;
      });

      const raiseData = await this.prisma.giveRaise.findMany({
        where: {
          userId: user.userId,
        },
      });

      const vettingResultData = await this.prisma.vettingResults.findMany({
        where: {
          userId: user.userId,
        },
      });

      const weeklySummaries = await this.prisma.weeklySummaries.findMany({
        where: {
          userId: user.userId,
        },
      });

      const educationDetails = await this.prisma.educationDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const experienceDetails = await this.prisma.experienceDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      hiredData.push({
        userData: user,
        monthlySalary: monthlyPayment,
        bonusHistory: bonusData,
        raiseHistory: raiseData,
        totalBonusGiven: totalBonus,
        vettingResult: vettingResultData,
        weeklySummaries: weeklySummaries,
        educationDetails: educationDetails,
        experienceDetails: experienceDetails,
      });
    }
    return hiredData;
  }

  async getRecommendationData(userId: string) {

    // Get the most recent hire form for this specific user
    const hireForm = await this.prisma.hireTopEngineer.findFirst({
      where: {
        userId: userId,
      },
      orderBy: {
        id: 'desc', // Get the most recent one
      },
    });

    const selectedSkills = hireForm?.skill || [];

    console.log('User ID:', userId);
    console.log('Hire form found:', !!hireForm);
    console.log('Selected skills from hire form:', selectedSkills);

    // If no hire form found or no skills selected, return empty arrays
    if (!hireForm || selectedSkills.length === 0) {
      console.log('No hire form or skills found, returning empty arrays');
      return { recommand: [], others: [] };
    }

    const userData = await this.prisma.user.findMany({
      where: {
        currentStatus: 'openToWork',
      },
    });
    const recommand: any[]=[];
    const others: any[]=[];

    for (const user of userData) {
      const userTechStack = user.techStack || [];

      // Improved skill matching - case insensitive and trimmed
      const isMatch = userTechStack.some((userSkill: string) =>
        selectedSkills.some((selectedSkill: string) =>
          userSkill.toLowerCase().trim() === selectedSkill.toLowerCase().trim()
        )
      );

      console.log(`User ${user.firstName} ${user.lastName}:`);
      console.log('  Tech stack:', userTechStack);
      console.log('  Is match:', isMatch);
      const hourlyRate = user.hourlyRate || 0;
      const workingHoursPerDay = 8;
      const workingDaysPerMonth = 22;
      const monthlyPayment =
        hourlyRate * workingHoursPerDay * workingDaysPerMonth;

      const vettingResultData = await this.prisma.vettingResults.findMany({
        where: {
          userId: user.userId,
        },
      });

      const educationDetails = await this.prisma.educationDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const experienceDetails = await this.prisma.experienceDetails.findMany({
        where: {
          userId: user.userId,
        },
      });

      const userObject = {
        userData: user,
        monthlyPayment: monthlyPayment,
        vettingResults: vettingResultData,
        educationDetails: educationDetails,
        experienceDetails: experienceDetails,
      };

      if (isMatch) {
        recommand.push(userObject);
      } else {
        others.push(userObject);
      }
    }

    console.log(`Final results: ${recommand.length} recommended, ${others.length} others`);
    return { recommand, others };
  }

  async giveBonusData(giveBonusDto: GiveBonusDto, userId: string) {
    const bonusData = await this.prisma.giveBonus.create({
      data: {
        userId: userId,
        bonusAmount: Number(giveBonusDto.bonusAmount),
        date: new Date(),
      },
    });
    return bonusData;
  }

  async giveRaiseData(giveRaiseDto: GiveRaiseDto, userId: string) {
    const raiseData = await this.prisma.giveRaise.create({
      data: {
        userId: userId,
        raiseAmount: giveRaiseDto.raiseAmount,
        effectiveOn: new Date(giveRaiseDto.effectiveOn),
        currentRate: giveRaiseDto.currentRate,
        afterRaiseRate: giveRaiseDto.afterRaiseRate,
        messageRegardingRaise: giveRaiseDto.messageRegardingRaise,
        sentOnDate: new Date(),
      },
    });
    return raiseData;
  }

  async getBonusData(userId: string) {
    if (userId) {
      const bonusData = await this.prisma.giveBonus.findMany({
        where: {
          userId: userId,
        },
      });
      return bonusData;
    }
  }

  async getRaiseData(userId: string) {
    if (userId) {
      const raiseData = await this.prisma.giveRaise.findMany({
        where: {
          userId: userId,
        },
      });
      return raiseData;
    }
  }
}
