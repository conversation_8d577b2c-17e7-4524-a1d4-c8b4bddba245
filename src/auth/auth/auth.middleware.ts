import { Constants, endpoints } from 'src/utils/constants';
import { Injectable, NestMiddleware } from '@nestjs/common';

import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private jwtService: JwtService) {}

  async use(req: any, res: any, next: () => void) {
    const authHeader = req.headers.authorization;
    if (
      req.originalUrl.startsWith(endpoints.user + endpoints.login) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.forgotPassword) ||
      req.originalUrl.startsWith(
        endpoints.applyAsEngineer + endpoints.addData,
      ) ||
      req.originalUrl.startsWith(
        endpoints.hiretopengineer + endpoints.addHireData,
      ) ||
      req.originalUrl.startsWith(
        endpoints.home + endpoints.addCommunityDetails,
      ) ||
      req.originalUrl.startsWith(
        endpoints.home + endpoints.getSuccessStoryDetails,
      ) ||
      req.originalUrl.startsWith(
        endpoints.home + endpoints.getCommunityDetails,
      ) ||
      req.originalUrl.startsWith(
        endpoints.home + endpoints.getTrustedByLogoData,
      ) ||
      req.originalUrl.startsWith(
        endpoints.user + endpoints.sendOTPforForgotPassword,
      ) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.addCertificate) ||
      req.originalUrl.startsWith(endpoints.home + endpoints.getClientReview) ||
      req.originalUrl.startsWith(
        endpoints.home + endpoints.getAvailableHireDeveloper,
      ) ||
      req.originalUrl.startsWith(endpoints.home + endpoints.getTeamDetails) ||
      req.originalUrl.startsWith(
        endpoints.devdashboard + endpoints.getBenefitsList,
      ) ||
      req.originalUrl.startsWith(
        endpoints.devdashboard + endpoints.getFAQData,
      ) ||
      req.originalUrl.startsWith(
        endpoints.devdashboard + endpoints.getBenefitsListQuestion,
      ) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.searchTalent) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.requestInterview) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.getUserData) ||
      req.originalUrl.startsWith(endpoints.gptVetting + endpoints.add) ||
      req.originalUrl.startsWith(endpoints.gptVetting + endpoints.update) ||
      req.originalUrl.startsWith(endpoints.gptVetting + endpoints.get) ||
      req.originalUrl.startsWith(endpoints.gptVetting + endpoints.sendEmail) ||
      req.originalUrl.startsWith(
        endpoints.gptVetting + endpoints.generateQuestions,
      ) ||
      req.originalUrl.startsWith(endpoints.gptVetting + endpoints.submit) ||
      req.originalUrl.startsWith(endpoints.user + endpoints.register) ||
      req.originalUrl.startsWith('/gptVetting/signed-url') ||
      req.originalUrl.startsWith(endpoints.ats) ||
      req.originalUrl.startsWith(endpoints.webhook + endpoints.stripe) ||
      req.originalUrl.startsWith('/gptVetting/uploadCSVCandidate')
    ) {
      next();
      return;
    }

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.slice(7);
      try {
        const decoded = this.jwtService.verify(token);
        req.user = decoded;
        next();
      } catch (err) {
        res.status(401).send(Constants.error);
      }
    } else {
      res.status(401).send(Constants.error);
    }
  }
}
