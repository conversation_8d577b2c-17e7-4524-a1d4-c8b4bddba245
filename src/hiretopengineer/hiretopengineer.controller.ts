import {
  Body,
  Controller,
  Get,
  Headers,
  HttpStatus,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { HiretopengineerService } from './hiretopengineer.service';
import { Constants, endpoints } from 'src/utils/constants';
import { ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { AddHireTopEngineerDto } from './dto/addhiretopengineer.dto';
import { Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { decodeJwtToken, extractTokenFromHeader } from 'src/utils/functions';

@ApiTags(endpoints.hiretopengineer)
@Controller(endpoints.hiretopengineer)
export class HiretopengineerController {
  constructor(
    private hiretopengineerService: HiretopengineerService,
    private jwtService: JwtService,
  ) {}

  @UseGuards(AuthGuard(Constants.JWT))
  @Post(endpoints.addHireData)
  @ApiSecurity('JWT-auth')
  async addhireEngineerData(
    @Headers('authorization') authorization: string,
    @Res() response: Response,
    @Body() addHireTopEngineerDto: AddHireTopEngineerDto,
  ) {
    try {
      if (!authorization) {
        return response.status(HttpStatus.UNAUTHORIZED).json({
          message: 'Authorization header is required',
        });
      }

      const token = extractTokenFromHeader(authorization);
      const userId = decodeJwtToken(token, this.jwtService);

      if (!userId) {
        return response.status(HttpStatus.UNAUTHORIZED).json({
          message: 'Invalid token: user ID not found',
        });
      }

      const hireEngineerData =
        await this.hiretopengineerService.addhireEngineerData(
          addHireTopEngineerDto,
          userId,
        );
      return response.status(HttpStatus.CREATED).json({
        message: Constants.addHireEngineerDataSuccess,
        hireEngineerData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.addHireEngineerDataError,
        error: err.message,
      });
    }
  }

  @Get(endpoints.getHireData)
  @ApiSecurity('JWT-auth')
  @ApiQuery({ name: 'id', required: false })
  async gethireEngineerData(
    @Res() response: Response,
    @Query('id') id?: string,
  ) {
    try {
      const hireEngineerData =
        await this.hiretopengineerService.gethireEngineerData(id);
      return response.status(HttpStatus.CREATED).json({
        message: Constants.getHireEngineerDataSuccess,
        hireEngineerData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.getHireEngineerDataError,
        error: err.message,
      });
    }
  }

  @Post(endpoints.deleteHireData)
  @ApiSecurity('JWT-auth')
  @ApiQuery({ name: 'id', required: false })
  async deletehireEngineerData(
    @Res() response: Response,
    @Query('id') id?: string,
  ) {
    try {
      const hireEngineerData =
        await this.hiretopengineerService.deletehireEngineerData(id);
      return response.status(HttpStatus.CREATED).json({
        message: Constants.deleteHireEngineerDataSuccess,
        hireEngineerData,
      });
    } catch (err) {
      return response.status(HttpStatus.BAD_REQUEST).json({
        message: Constants.deleteHireEngineerDataError,
        error: err.message,
      });
    }
  }
}
