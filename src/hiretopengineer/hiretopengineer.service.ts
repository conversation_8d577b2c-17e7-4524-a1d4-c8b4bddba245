import * as AWS from 'aws-sdk';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs-extra';
import * as path from 'path';

import { AddHireTopEngineerDto } from './dto/addhiretopengineer.dto';
import { Constants } from 'src/utils/constants';
import { GoogleAuth } from 'google-auth-library';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { ROOT_DIR } from 'rootDir';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class HiretopengineerService {
  private readonly storage: Storage;
  private readonly bucketName: string;
  private readonly doc: GoogleSpreadsheet;
  constructor(private prisma: PrismaService) {
    const filePath = path.resolve(ROOT_DIR, 'eremote-hire-website-cloud.json');
    const credentials = fs.readFileSync(filePath, 'utf8');

    this.storage = new Storage({
      projectId: process.env.PROJECT_ID,
      keyFilename: process.env.KEYFILENAME,
    });
    this.bucketName = process.env.CLOUD_BUCKET_NAME;
    const auth = new GoogleAuth({
      credentials: JSON.parse(credentials),
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    this.doc = new GoogleSpreadsheet(
      process.env.SPREADSHEET_ID,
      auth
    );
  }

  async addhireEngineerData(
    addHireTopEngineerDto: AddHireTopEngineerDto,
    userId?: string
  ) {
    try {
      await this.doc.loadInfo();
      const sheet = this.doc.sheetsByIndex[0];
      console.log('user_id', userId);
      const hiredData = await this.prisma.hireTopEngineer.create({
        data: {
          userId: userId || null,
          workType: addHireTopEngineerDto.workType,
          skill: addHireTopEngineerDto.skill || [],
          noOfSoftEngineer: addHireTopEngineerDto.noOfSoftEngineer,
          firstName: addHireTopEngineerDto.firstName,
          lastName: addHireTopEngineerDto.lastName,
          companyEmail: addHireTopEngineerDto.companyEmail,
          noOfEmployee: addHireTopEngineerDto.noOfEmployee,
          message: addHireTopEngineerDto.message || null,
          findUs: addHireTopEngineerDto.findUs || [],
        },
      });

      const skillData = hiredData.skill.join(', ');
      const findUsData = hiredData.findUs.join(', ');

      const rowData = [
        hiredData.workType,
        skillData,
        hiredData.noOfSoftEngineer,
        hiredData.firstName,
        hiredData.lastName,
        hiredData.companyEmail,
        hiredData.noOfEmployee,
        hiredData.message || 'null',
        findUsData,
      ];

      await sheet.addRow(rowData);

      return hiredData;
    } catch (error) {
      console.error('Error adding hire top engineer data:', error);
      throw error;
    }
  }

  private async checkIfFileExists(bucketName: string, fileName: string) {
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    return new Promise((resolve, reject) => {
      file.exists((err, exists) => {
        if (err) {
          reject(err);
        } else {
          resolve(exists);
        }
      });
    });
  }

  private async downloadFileFromGCS(
    bucketName: string,
    fileName: string,
  ): Promise<Buffer> {
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    return new Promise((resolve, reject) => {
      file.download((err, contents) => {
        if (err) {
          reject(err);
        } else {
          resolve(contents);
        }
      });
    });
  }

  async gethireEngineerData(id?: string) {
    if (id) {
      const hireData = await this.prisma.hireTopEngineer.findUnique({
        where: {
          id: id,
        },
      });
      return hireData;
    } else {
      const hireData = await this.prisma.hireTopEngineer.findMany({});
      return hireData;
    }
  }

  async deletehireEngineerData(id?: string) {
    if (id) {
      const hireData = await this.prisma.hireTopEngineer.delete({
        where: {
          id: id,
        },
      });
      return hireData;
    } else {
      const hireData = await this.prisma.hireTopEngineer.deleteMany({});
      return hireData;
    }
  }
}
