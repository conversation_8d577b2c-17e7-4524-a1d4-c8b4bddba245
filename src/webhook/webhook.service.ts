import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import <PERSON><PERSON> from 'stripe';

@Injectable()
export class WebhookService {
  constructor(private prisma: PrismaService) {}

  // async handleStripeWebhook(event: Stripe.Event) {
  //     switch (event.type) {
  //         case 'checkout.session.completed':
  //             const session = event.data.object as Stripe.Checkout.Session;
  //             const userId = session.client_reference_id; // Pass user ID in the payment link
  //             const planType = session.metadata.planType; // Pass plan type in the payment link

  //             // Update the database
  //             const subscriptionSuccess = await this.prisma.subscription.create({
  //                 data: {
  //                     userId,
  //                     planType,
  //                     paymentStatus: 'paid',
  //                     startDate: new Date(),
  //                     endDate: this.calculateEndDate(planType), // Calculate end date based on plan type
  //                     stripePaymentIntentId: session.payment_intent as string,
  //                 },
  //             });

  //             console.log(subscriptionSuccess, "subscriptionSuccess");
  //             break;

  //         default:
  //             console.log(`Unhandled event type: ${event.type}`);
  //     }

  //     return { success: true };
  // }

  async handleStripeWebhook(payload: any) {
    console.dir({ payload }, { depth: null });
    switch (payload.type) {
      case 'checkout.session.completed':
        const email = payload.data.object.customer_details.email;
        const phone = payload.data.object.customer_details.phone;

        const findUser = await this.prisma.user.findFirst({
          where: {
            OR: [
              {
                emailId: {
                  contains: email,
                  mode: 'insensitive',
                },
              },
              {
                phoneNo: {
                  equals: phone ? parseInt(phone, 10) : null,
                },
              },
            ],
          },
        });

        if (!findUser) {
          throw new Error('User not found');
        }

        let planType = 'monthly';
        let seat = 5;

        if (payload.data.object?.metadata?.planType) {
          planType = payload.data.object.metadata.planType;
        }

        if (payload.data.object?.metadata?.seat) {
          seat = Number(payload.data.object.metadata.seat);
        }

        const subscriptionSuccess = await this.prisma.subscriptions.create({
          data: {
            userId: findUser.userId,
            planType,
            paymentStatus: payload.data.object.payment_status,
            startDate: new Date(),
            endDate: this.calculateEndDate(planType),
            stripePaymentIntentId: payload.data.object.payment_intent as string,
            isActive: true,
            earlyRenewal: false,
            seat,
          },
        });

        let totalSeat = seat
        if (findUser?.subscription?.seat) {
          totalSeat = findUser.subscription.seat + seat
        }
        console.log({ totalSeat });

        try {
          
         const response =  await this.prisma.user.update({
            where: { userId: findUser.userId },
            data: {
              subscription: {
                planType: subscriptionSuccess.planType,
                status: subscriptionSuccess.paymentStatus,
                seat: parseInt(totalSeat.toString(), 10),
              },
            },
          });
          console.dir({ response }, {depth: null});
        } catch (error) {
          console.dir({ error }, {depth: null});
          
        }
        
        break;

      default:
        console.log(`Unhandled event type: ${payload.type}`);
    }
    return { success: true };
  }

  private calculateEndDate(planType: string): Date {
    const now = new Date();
    if (planType === 'monthly') {
      return new Date(now.setMonth(now.getMonth() + 1));
    } else if (planType === 'yearly') {
      return new Date(now.setFullYear(now.getFullYear() + 1));
    }
    throw new Error('Invalid plan type');
  }
}
