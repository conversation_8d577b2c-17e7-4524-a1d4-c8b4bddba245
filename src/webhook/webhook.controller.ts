import {
  <PERSON>,
  HttpStatus,
  <PERSON>,
  Req,
  <PERSON><PERSON>,
  <PERSON>ers,
} from '@nestjs/common';
import { Constants, endpoints } from 'src/utils/constants';
import { WebhookService } from './webhook.service';
import Stripe from 'stripe';
import { Request, Response } from 'express';
import { buffer } from 'rxjs';
import { ApiProperty } from '@nestjs/swagger';

@Controller(endpoints.webhook)
export class WebhookController {
  private readonly stripe: Stripe;

  constructor(private webhookService: WebhookService) {
    // Initialize Stripe
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-03-31.basil' as any
    });
  }

  @Post(endpoints.stripe)
  @ApiProperty()
  async handleStripeWebhook(
    @Req() request: Request,
    @Res() response: Response,
  ) {
    const payload = request.body;
    try {
      const result = await this.webhookService.handleStripeWebhook(payload);
      return response.status(HttpStatus.OK).json({ received: true, result });
    } catch (err) {
      console.error('Webhook error:', err.message);
      return response
        .status(HttpStatus.BAD_REQUEST)
        .json({ error: err.message });
    }
  }
}
