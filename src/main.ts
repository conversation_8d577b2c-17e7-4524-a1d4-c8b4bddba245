import * as bodyParser from 'body-parser';
import * as express from 'express';

import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { AppModule } from './app.module';
import { NestFactory } from '@nestjs/core';
import { endpoints } from './utils/constants';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.use(express.json({ limit: '500mb' }));
  app.use(express.urlencoded({ limit: '500mb', extended: true }));

  app.use(bodyParser.json({ limit: '500mb' }));
  app.use(bodyParser.urlencoded({ limit: '500mb', extended: true }));

  const config = new DocumentBuilder()
    .setTitle('Remote Hire')
    .setDescription('The remoteHire API description')
    .setVersion('1.0')
    .addTag(endpoints.user, 'Endpoints related to user')
    .addTag(endpoints.applyAsEngineer, 'Endpoints related to applyAsEngineer')
    .addTag(endpoints.myteam, 'Endpoints related to myteam')
    .addTag(endpoints.devdashboard, 'Endpoints related to devdashboard')
    .addTag(endpoints.document, 'Endpoints related to document')
    .addTag(endpoints.hiretopengineer, 'Endpoints related to hiretopengineer')
    .addTag(endpoints.home, 'Endpoints related to home')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT Token',
        in: 'header',
      },
      'JWT-auth',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    customSiteTitle: '',
  });
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
  });
  app.enableShutdownHooks();
  await app.listen(4000);
  // const port = process.env.PORT || 3000;
  // await app.listen(port, '0.0.0.0');
  // console.log(`🚀 Server is running on port ${port}`);

}
bootstrap();
