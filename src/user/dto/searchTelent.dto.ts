import { ApiProperty } from '@nestjs/swagger';
import { typeOfEngagement } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class SearchTalentDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  search: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  page: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  limit: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  requestForInterview: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  sortBy?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  technicalSkills?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  softSkills?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  pricePerHour?: number;

  @ApiProperty()
  @IsEnum(typeOfEngagement)
  @IsOptional()
  availability?: typeOfEngagement;
}
