import { IsBoolean, IsEmail, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { currentStatus, typeOfEngagement, userRole } from '@prisma/client';

import { ApiProperty } from '@nestjs/swagger';

class SocialLinksDto {
  @ApiProperty()
  facebook: string;

  @ApiProperty()
  linkedIn: string;

  @ApiProperty()
  twitter: string;
}

export class AddUserDto {
  @IsString()
  @ApiProperty()
  userId: string;

  @ApiProperty()
  @IsString()
  firstName?: string;

  @ApiProperty()
  @IsString()
  lastName?: string;

  @ApiProperty()
  @IsNumber()
  age?: number;

  @ApiProperty()
  @IsString()
  designation?: string;

  @ApiProperty()
  @IsString({ each: true })
  techStack?: string[];

  @ApiProperty()
  @IsEmail()
  emailId: string;

  @ApiProperty()
  @IsEmail()
  password: string;

  @ApiProperty()
  @IsNumber()
  hourlyRate?: number;

  @ApiProperty()
  @IsString()
  country?: string;

  @ApiProperty()
  @IsString()
  address?: string;

  @ApiProperty()
  @IsNumber()
  phoneNo: number;

  @ApiProperty()
  @IsNumber()
  yearOfExperience?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  resume?: string;

  @ApiProperty()
  typeOfEngagement?: typeOfEngagement;

  @ApiProperty()
  currentStatus?: currentStatus;

  @ApiProperty()
  @IsString()
  noticePeriod?: string;

  @ApiProperty()
  @IsString()
  summary?: string;

  @ApiProperty()
  @IsString()
  certificate?: string;

  @ApiProperty()
  @IsString()
  socialLinks?: SocialLinksDto;

  @ApiProperty()
  userRole: userRole;

  @ApiProperty()
  @IsBoolean()
  contected?: boolean;

  @ApiProperty()
  @IsString()
  companyName?: string;
}
