import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ApplyAsEngineerController } from './apply-as-engineer/applyasengineer.controller';
import { ApplyAsEngineerModule } from './apply-as-engineer/applyasengineer.module';
import { ApplyAsEngineerService } from './apply-as-engineer/applyasengineer.service';
import { AtsController } from './ats/ats.controller';
import { AtsModule } from './ats/ats.module';
import { AtsService } from './ats/ats.service';
import { AuthMiddleware } from './auth/auth/auth.middleware';
import { AuthModule } from './auth/auth/auth.module';
import { AuthService } from './auth/auth/auth.service';
import { ClientdashboardController } from './clientdashboard/clientdashboard.controller';
import { ClientdashboardModule } from './clientdashboard/clientdashboard.module';
import { ClientdashboardService } from './clientdashboard/clientdashboard.service';
import { DevdashboardController } from './devdashboard/devdashboard.controller';
import { DevdashboardModule } from './devdashboard/devdashboard.module';
import { DevdashboardService } from './devdashboard/devdashboard.service';
import { DocumentController } from './document/document.controller';
import { DocumentModule } from './document/document.module';
import { DocumentService } from './document/document.service';
import { EmailCronService } from './email-cron.service';
import { GptVettingController } from './gptVetting/gptVetting.controller';
import { GptVettingModule } from './gptVetting/gptVetting.module';
import { GptVettingService } from './gptVetting/gptVetting.service';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { HiretopengineerController } from './hiretopengineer/hiretopengineer.controller';
import { HiretopengineerModule } from './hiretopengineer/hiretopengineer.module';
import { HiretopengineerService } from './hiretopengineer/hiretopengineer.service';
import { HomePageController } from './homePage/homePage.controller';
import { HomePageModule } from './homePage/homePage.module';
import { HomePageService } from './homePage/homePage.service';
import { HttpModule } from '@nestjs/axios';
import { MailerModule } from '@nestjs-modules/mailer';
import { MyteamController } from './myTeam/myteam.controller';
import { MyteamModule } from './myTeam/myteam.module';
import { MyteamService } from './myTeam/myteam.service';
import { PrismaService } from './prisma.service';
import { ScheduleModule } from '@nestjs/schedule';
import { Storage } from '@google-cloud/storage';
import { UserController } from './user/user.controller';
import { UserModule } from './user/user.module';
import { UserService } from './user/user.service';
import { WebhookController } from './webhook/webhook.controller';
import { WebhookModule } from './webhook/webhook.module';
import { WebhookService } from './webhook/webhook.service';
import { urlencoded } from 'express';

@Module({
  imports: [
    AuthModule,
    UserModule,
    DocumentModule,
    HttpModule,
    MyteamModule,
    ScheduleModule.forRoot(),
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com',
        // auth: {
        //   user: '<EMAIL>',
        //   pass: 'HTESX7KgAxVk',
        // },
        // auth: {
        //   user: '<EMAIL>',
        //   pass: 'rngw lbvg fwpb keib',
        // },
        auth: {
          user: '<EMAIL>',
          pass: 'otne xixw xokg eanb',
        },
      },
      template: {
        dir: process.cwd() + '/dist/templates/',
        adapter: new HandlebarsAdapter(),
      },
    }),
    ApplyAsEngineerModule,
    HiretopengineerModule,
    HomePageModule,
    DevdashboardModule,
    ClientdashboardModule,
    GptVettingModule,
    AtsModule,
    WebhookModule,
  ],
  controllers: [
    AppController,
    UserController,
    DocumentController,
    MyteamController,
    ApplyAsEngineerController,
    HiretopengineerController,
    HomePageController,
    DevdashboardController,
    ClientdashboardController,
    GptVettingController,
    AtsController,
    WebhookController,
  ],
  providers: [
    AppService,
    PrismaService,
    UserService,
    DocumentService,
    EmailCronService,
    AuthService,
    MyteamService,
    ApplyAsEngineerService,
    HiretopengineerService,
    HomePageService,
    DevdashboardService,
    ClientdashboardService,
    Storage,
    GptVettingService,
    AtsService,
    WebhookService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(urlencoded({ extended: true }), AuthMiddleware)
      .forRoutes('*');
  }
}
